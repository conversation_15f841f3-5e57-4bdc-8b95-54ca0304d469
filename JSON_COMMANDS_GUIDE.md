# Руководство по JSON командам CORDON-82

## ✅ ИСПРАВЛЕНО: JSON команды теперь отправляют ответы!

### 🔧 **Что было исправлено:**

1. **Добавлены JSON ответы** для всех команд
2. **Добавлено эхо JSON команды** в основном цикле
3. **Добавлены ответы об ошибках** для неизвестных команд и ошибок парсинга

## 📋 **Доступные JSON команды:**

### 1. **Команда готовности:**
```json
{"cmd":"ready"}
```
**Ответ:**
```json
{"status":"ready","command":"ready","system":"cordon-82"}
```

### 2. **Информация о системе:**
```json
{"cmd":"about"}
```
**Ответ:**
```json
{"status":"ok","command":"about","version":"1.0","device":"CORDON-82"}
```

### 3. **Тест мотора M6:**
```json
{"cmd":"test_m6"}
```
**Ответ:**
```json
{"status":"ok","command":"test_m6","result":"executed"}
```

### 4. **Сброс системы:**
```json
{"cmd":"reset"}
```
**Ответ:**
```json
{"status":"ok","command":"reset","result":"executed"}
```

### 5. **Воспроизведение мелодии:**
```json
{"cmd":"play_melody"}
```
**Ответ:**
```json
{"status":"ok","command":"play_melody","result":"executed"}
```

## 🧪 **Тестирование через WebSocket:**

### Последовательность тестирования:

1. **Подключитесь к WebSocket**
2. **Отправьте команду готовности:**
   ```json
   {"cmd":"ready"}
   ```
3. **Ожидаемый ответ:**
   ```json
   {"status":"ready","command":"ready","system":"cordon-82"}
   ```

4. **Протестируйте M6:**
   ```json
   {"cmd":"test_m6"}
   ```
5. **Ожидаемый ответ:**
   ```json
   {"status":"ok","command":"test_m6","result":"executed"}
   ```

## 🔍 **Диагностика проблем:**

### Если получаете странные символы (0xFF):
1. **Проверьте формат команды** - должна быть валидный JSON
2. **Убедитесь что команда заканчивается }**
3. **Проверьте что команда начинается с {**

### Если команда не выполняется:
1. **Проверьте имя команды** - должно точно совпадать
2. **Проверьте синтаксис JSON** - кавычки, запятые
3. **Посмотрите на LCD** - там отображается статус обработки

### Возможные ответы об ошибках:

#### Неизвестная команда:
```json
{"status":"error","message":"unknown command"}
```

#### Ошибка парсинга JSON:
```json
{"status":"error","message":"parse error"}
```

## 📊 **Формат ответов:**

### Успешное выполнение:
```json
{
  "status": "ok",
  "command": "имя_команды",
  "result": "executed"
}
```

### Специальные ответы:

#### Готовность системы:
```json
{
  "status": "ready",
  "command": "ready",
  "system": "cordon-82"
}
```

#### Информация о системе:
```json
{
  "status": "ok",
  "command": "about",
  "version": "1.0",
  "device": "CORDON-82"
}
```

### Ошибки:
```json
{
  "status": "error",
  "message": "описание_ошибки"
}
```

## 🎯 **Рекомендуемая последовательность тестирования:**

1. **Базовая проверка связи:**
   ```json
   {"cmd":"ready"}
   ```

2. **Информация о системе:**
   ```json
   {"cmd":"about"}
   ```

3. **Простой тест мотора:**
   ```json
   {"cmd":"test_m6"}
   ```

4. **Тест звука:**
   ```json
   {"cmd":"play_melody"}
   ```

5. **Тест неизвестной команды:**
   ```json
   {"cmd":"unknown_test"}
   ```

## 🔧 **Технические детали:**

### Обработка JSON команд:
1. **Прием команды** через UART
2. **Отправка эхо** команды обратно
3. **Парсинг JSON** строки
4. **Выполнение команды** 
5. **Отправка JSON ответа**

### Поддерживаемые команды:
- `ready` - проверка готовности
- `about` - информация о системе
- `reset` - сброс всех моторов
- `play_melody` - воспроизведение мелодии
- `test_m6` - тест мотора M6
- `fire` - команда стрельбы (TODO)
- `test_all` - тест всех систем
- `move_motor` - движение мотора
- `set_speed` - установка скорости
- `get_status` - получение статуса
- `stop_all` - остановка всех моторов
- `aim` - наведение
- `set_position` - установка позиции
- `auto_calibrate` или `ac` - автокалибровка

## ✅ **Статус исправления:**

**JSON команды исправлены и готовы к работе!**

Теперь все JSON команды отправляют корректные ответы вместо странных символов 0xFF.
