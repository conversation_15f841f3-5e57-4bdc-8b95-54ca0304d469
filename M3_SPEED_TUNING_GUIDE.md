# Руководство по настройке скорости мотора M3 (Каретка)

## ⚠️ ПРОБЛЕМА: M3 уходит в ошибку драйвера

**Причина:** Слишком агрессивные настройки (80 мкс) вызывают ошибку драйвера мотора.

**Решение:** Постепенная настройка скорости с тестированием каждого уровня.

## 🔧 Исправленные настройки

### Текущие БЕЗОПАСНЫЕ настройки M3:
- `step_delay_us`: **250 мкс** (было 80 мкс)
- `pulse_width_us`: **250 мкс** (было 80 мкс)
- `max_speed_hz`: **2000 Гц** (было 6250 Гц)
- Период: **500 мкс** (250 мкс импульс + 250 мкс пауза)

## 🧪 Команды для тестирования скорости M3

### Постепенное увеличение скорости:

#### 1. Консервативная скорость (БЕЗОПАСНО):
```
$220xxxx;  // 500 мкс = 1000 Гц
```
- Самая безопасная настройка
- Гарантированно работает
- Используйте для начального тестирования

#### 2. Средняя скорость:
```
$221xxxx;  // 350 мкс = 1428 Гц
```
- Умеренное увеличение скорости
- Хороший компромисс скорость/надежность

#### 3. Быстрая скорость (ТЕКУЩАЯ):
```
$222xxxx;  // 250 мкс = 2000 Гц
```
- **Текущая настройка по умолчанию**
- Проверенная безопасная скорость
- Рекомендуется для постоянного использования

#### 4. Очень быстрая скорость:
```
$223xxxx;  // 200 мкс = 2500 Гц
```
- Высокая скорость
- **ТЕСТИРУЙТЕ ОСТОРОЖНО!**
- Проверьте стабильность работы

#### 5. Ультра быстрая скорость:
```
$224xxxx;  // 150 мкс = 3333 Гц
```
- Очень высокая скорость
- **РИСК ОШИБОК ДРАЙВЕРА!**
- Тестируйте короткими импульсами

#### 6. Экстремальная скорость:
```
$225xxxx;  // 100 мкс = 5000 Гц
```
- **МАКСИМАЛЬНАЯ СКОРОСТЬ!**
- **ВЫСОКИЙ РИСК ОШИБОК!**
- Только для экспериментов

## 📋 Протокол тестирования

### Пошаговая процедура:

1. **Начните с команды 220** (500 мкс)
   - Проверьте работу мотора
   - Убедитесь в отсутствии ошибок

2. **Переходите к команде 221** (350 мкс)
   - Тестируйте несколько операций
   - Следите за поведением драйвера

3. **Используйте команду 222** (250 мкс)
   - **Рекомендуемая настройка**
   - Оптимальный баланс скорость/надежность

4. **Осторожно тестируйте 223-225**
   - Только если нужна максимальная скорость
   - Готовьтесь к возможным ошибкам

### Признаки проблем:
- Ошибки драйвера мотора
- Пропуск шагов
- Странные звуки или вибрации
- Нестабильное позиционирование

### Действия при ошибках:
1. **Немедленно вернитесь к команде 220** (безопасная скорость)
2. **Сбросьте защиту мотора**: `$213xxxx;`
3. **Проверьте механику** на заедания
4. **Дайте системе остыть** 30-60 секунд

## 🎯 Рекомендуемые настройки

### Для разных режимов работы:

#### Режим отладки/тестирования:
```
$220xxxx;  // 500 мкс = 1000 Гц
```
- Максимальная надежность
- Легко диагностировать проблемы

#### Рабочий режим (рекомендуется):
```
$222xxxx;  // 250 мкс = 2000 Гц
```
- Хорошая скорость
- Проверенная стабильность
- **ТЕКУЩАЯ НАСТРОЙКА ПО УМОЛЧАНИЮ**

#### Высокопроизводительный режим:
```
$223xxxx;  // 200 мкс = 2500 Гц
```
- Только после тщательного тестирования
- Контролируйте температуру
- Следите за ошибками

## ⚡ Технические детали

### Ограничения драйвера:
- Минимальная длительность импульса: ~100-150 мкс
- Максимальная частота: ~3000-5000 Гц (зависит от драйвера)
- Время нарастания/спада сигнала: ~10-20 мкс

### Факторы, влияющие на максимальную скорость:
1. **Тип драйвера** - разные драйверы имеют разные ограничения
2. **Напряжение питания** - высокое напряжение = выше скорость
3. **Индуктивность мотора** - низкая индуктивность = выше скорость
4. **Нагрузка на мотор** - высокая нагрузка = ниже максимальная скорость
5. **Температура** - перегрев снижает производительность

## 🔍 Диагностика проблем

### Команды диагностики:
```
$210xxxx;  // Статус системы защиты
$213xxxx;  // Сброс защиты M3
```

### Проверка текущих настроек:
После выполнения команд 220-225, текущие значения:
- `M3_StepDelay_uS` - задержка в микросекундах
- `M3_PulseWidth_uS` - ширина импульса в микросекундах

### Мониторинг:
- Следите за сообщениями на LCD
- Контролируйте звуковые сигналы ошибок
- Проверяйте точность позиционирования

## 📊 Сравнение производительности

| Команда | Задержка | Частота | Скорость | Надежность | Рекомендация |
|---------|----------|---------|----------|------------|--------------|
| 220     | 500 мкс  | 1000 Гц | Низкая   | Максимальная | Отладка |
| 221     | 350 мкс  | 1428 Гц | Средняя  | Высокая    | Тестирование |
| **222** | **250 мкс** | **2000 Гц** | **Хорошая** | **Высокая** | **РЕКОМЕНДУЕТСЯ** |
| 223     | 200 мкс  | 2500 Гц | Высокая  | Средняя    | Осторожно |
| 224     | 150 мкс  | 3333 Гц | Очень высокая | Низкая | Эксперименты |
| 225     | 100 мкс  | 5000 Гц | Максимальная | Очень низкая | Риск |

## ✅ Итоговые рекомендации

1. **Используйте команду 222** как основную настройку (250 мкс, 2000 Гц)
2. **Тестируйте постепенно** при необходимости увеличения скорости
3. **Всегда имейте план отката** к безопасным настройкам
4. **Мониторьте систему** на предмет ошибок и перегрева
5. **Документируйте** оптимальные настройки для вашей системы

**Текущий статус:** M3 настроен на безопасную скорость 2000 Гц (250 мкс). Система защиты активна.
