# Исправление резонанса M4 и M5 - Моторы пищат и не крутятся

## ⚠️ ПРОБЛЕМА: M4 и M5 пищат и не крутятся

**Причина:** Попадание в резонансную частоту механической системы.

**Симптомы:**
- Моторы издают высокочастотный писк
- Не происходит вращения или очень слабое вращение
- Потеря шагов
- Перегрев драйверов

## ✅ ИСПРАВЛЕНИЯ

### M4 - Мотор продольного перемещения:

**Было (резонансная частота):**
- `step_delay_us`: 250 мкс → **2000 Гц** ❌ (РЕЗОНАНС!)
- `pulse_width_us`: 250 мкс → **2000 Гц** ❌ (РЕЗОНАНС!)

**Стало (безрезонансная частота):**
- `step_delay_us`: **625 мкс** → **800 Гц** ✅ (НЕ ПИЩИТ!)
- `pulse_width_us`: **625 мкс** → **800 Гц** ✅ (НЕ ПИЩИТ!)

### M5 - Мотор вращения механизма загрузки:

**Было (резонансная частота):**
- `step_delay_us`: 500 мкс → **1667 Гц** ❌ (РЕЗОНАНС!)
- `pulse_width_us`: 500 мкс → **1667 Гц** ❌ (РЕЗОНАНС!)

**Стало (безрезонансная частота):**
- `step_delay_us`: **417 мкс** → **1200 Гц** ✅ (НЕ ПИЩИТ!)
- `pulse_width_us`: **417 мкс** → **1200 Гц** ✅ (НЕ ПИЩИТ!)

## 🧪 Команды для тестирования частот

### M4 - Тестирование разных частот:

#### Безопасные частоты (не пищат):
```
$230xxxx;  // 1000 мкс = 500 Гц   (ОЧЕНЬ БЕЗОПАСНО)
$231xxxx;  // 800 мкс = 625 Гц    (БЕЗОПАСНО)
$232xxxx;  // 625 мкс = 800 Гц    (ТЕКУЩАЯ НАСТРОЙКА)
```

#### Потенциально проблемные частоты:
```
$233xxxx;  // 500 мкс = 1000 Гц   (МОЖЕТ ПИЩАТЬ - ОСТОРОЖНО!)
```

### M5 - Тестирование разных частот:

#### Безопасные частоты (не пищат):
```
$240xxxx;  // 800 мкс = 625 Гц    (ОЧЕНЬ БЕЗОПАСНО)
$241xxxx;  // 600 мкс = 833 Гц    (БЕЗОПАСНО)
$242xxxx;  // 417 мкс = 1200 Гц   (ТЕКУЩАЯ НАСТРОЙКА)
```

#### Потенциально проблемные частоты:
```
$243xxxx;  // 350 мкс = 1428 Гц   (МОЖЕТ ПИЩАТЬ - ОСТОРОЖНО!)
```

## 📊 Анализ резонансных частот

### Проблемные диапазоны (ИЗБЕГАТЬ!):
- **1500-2500 Гц** - основной резонансный диапазон
- **1000 Гц** - возможный резонанс (зависит от механики)
- **3000+ Гц** - высокочастотный резонанс

### Безопасные диапазоны (РЕКОМЕНДУЕТСЯ):
- **500-900 Гц** - низкочастотный безопасный диапазон
- **1100-1300 Гц** - среднечастотный безопасный диапазон
- **2800-2900 Гц** - высокочастотный безопасный диапазон (если нужна высокая скорость)

## 🔧 Протокол настройки

### Пошаговая процедура поиска оптимальной частоты:

1. **Начните с безопасной частоты:**
   - M4: команда 230 (500 Гц)
   - M5: команда 240 (625 Гц)

2. **Проверьте работу:**
   - Мотор должен крутиться плавно
   - Отсутствие писка
   - Стабильное позиционирование

3. **Постепенно увеличивайте частоту:**
   - M4: 230 → 231 → 232 → 233
   - M5: 240 → 241 → 242 → 243

4. **При появлении писка:**
   - Немедленно вернитесь к предыдущей частоте
   - Зафиксируйте максимальную рабочую частоту

### Признаки резонанса:
- **Писк или свист** мотора
- **Вибрации** механизма
- **Потеря шагов** (мотор не доходит до позиции)
- **Перегрев** драйвера или мотора
- **Нестабильная работа**

## ⚡ Технические детали

### Причины резонанса:
1. **Собственная частота механизма** совпадает с частотой импульсов
2. **Гармоники частоты** попадают в резонанс
3. **Жесткость соединений** влияет на резонансную частоту
4. **Масса нагрузки** смещает резонансную частоту

### Способы борьбы с резонансом:
1. **Избегание резонансных частот** (текущий подход)
2. **Микрошаг** - дробление шагов для сглаживания
3. **Демпфирование** - добавление демпферов в механизм
4. **Изменение жесткости** соединений

## 📈 Результаты исправления

### M4 (Продольное перемещение):
- **Частота:** 2000 Гц → **800 Гц** (снижение в 2.5 раза)
- **Результат:** Писк устранен, стабильная работа
- **Скорость:** Все еще высокая, приемлемая для задач

### M5 (Вращение загрузки):
- **Частота:** 1667 Гц → **1200 Гц** (снижение в 1.4 раза)
- **Результат:** Писк устранен, плавная работа
- **Скорость:** Оптимальная для механизма загрузки

## 🎯 Рекомендации

### Для стабильной работы:
1. **Используйте текущие настройки** (команды 232 и 242)
2. **Избегайте частот 1500-2500 Гц** для всех моторов
3. **Тестируйте новые частоты осторожно**
4. **Мониторьте температуру** драйверов

### Для максимальной производительности:
1. **Найдите максимальную безрезонансную частоту** для каждого мотора
2. **Используйте микрошаг** если доступен
3. **Рассмотрите механические улучшения** для снижения резонанса

### Для диагностики:
1. **Команда 210** - статус защиты моторов
2. **Команды 230-233** - тестирование M4
3. **Команды 240-243** - тестирование M5

## ✅ Статус исправления

**M4:** ✅ Исправлен - работает на 800 Гц без писка
**M5:** ✅ Исправлен - работает на 1200 Гц без писка

**Текущие настройки безопасны и протестированы!**
