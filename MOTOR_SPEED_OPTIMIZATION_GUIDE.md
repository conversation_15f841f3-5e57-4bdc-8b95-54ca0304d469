# Руководство по оптимизации скоростей моторов и защите от перегрузки

## 🚀 Максимальные скорости моторов

### M1 - Мотор горизонтальной наводки (NEMA23 + редуктор 1:40)
**ЭКСТРЕМАЛЬНЫЕ НАСТРОЙКИ:**
- `step_delay_us`: 100 мкс (было 1000 мкс)
- `pulse_width_us`: 100 мкс (было 1000 мкс)
- `max_speed_hz`: 5000 Гц (было 500 Гц) - **увеличение в 10 раз!**
- Частота импульсов: 5 кГц
- Период: 200 мкс (100 мкс импульс + 100 мкс пауза)

### M2 - Мотор вертикальной наводки (NEMA34 + редуктор 1:10)
**ЭКСТРЕМАЛЬНЫЕ НАСТРОЙКИ:**
- `step_delay_us`: 150 мкс (было 10000 мкс)
- `pulse_width_us`: 150 мкс (было 10000 мкс)
- `max_speed_hz`: 3333 Гц (было 50 Гц) - **увеличение в 66 раз!**
- Частота импульсов: 3.33 кГц
- Период: 300 мкс (150 мкс импульс + 150 мкс пауза)

### M3 - Мотор каретки (цепная передача)
**ЭКСТРЕМАЛЬНЫЕ НАСТРОЙКИ:**
- `step_delay_us`: 80 мкс (было 300 мкс)
- `pulse_width_us`: 80 мкс (было 300 мкс)
- `max_speed_hz`: 6250 Гц (было 1667 Гц) - **увеличение в 3.75 раза!**
- Частота импульсов: 6.25 кГц
- Период: 160 мкс (80 мкс импульс + 80 мкс пауза)

## 🛡️ Система защиты от перегрузки

### Принцип работы:
1. **Мониторинг времени операций** - каждая операция мотора отслеживается
2. **Подсчет сбоев** - ведется статистика успешных/неудачных операций
3. **Автоматическая защита** - при превышении лимитов мотор блокируется
4. **Звуковые сигналы** - предупреждения о перегрузке

### Настройки защиты:

#### Максимальное время операций:
- **M1**: 5000 мс (5 секунд)
- **M2**: 8000 мс (8 секунд) 
- **M3**: 3000 мс (3 секунды) - быстрый мотор
- **M4**: 4000 мс (4 секунды)
- **M5**: 4000 мс (4 секунды)
- **M6**: 6000 мс (6 секунд) - мощный мотор

#### Пороги срабатывания:
- **Последовательные сбои**: максимум 3 подряд
- **Общие сбои**: максимум 10 всего
- **Время охлаждения**: 30 секунд после срабатывания защиты

### Функции защиты:

#### Основные функции:
```c
Motor_Protection_Init()           // Инициализация системы защиты
Motor_Protection_Start(motor_id)  // Начало мониторинга операции
Motor_Protection_Stop(motor_id, success) // Завершение с результатом
Motor_Protection_Check(motor_id)  // Проверка во время операции
Motor_Protection_Reset(motor_id)  // Сброс защиты мотора
```

#### Диагностические функции:
```c
Motor_Protection_Diagnostics()    // Показать статус всех моторов
Motor_Protection_GetStatus(motor_id) // Получить статус конкретного мотора
```

## 📋 Команды для тестирования

### Диагностика защиты:
- `$210xxxx;` - Диагностика системы защиты всех моторов

### Сброс защиты:
- `$211xxxx;` - Сброс защиты мотора M1
- `$212xxxx;` - Сброс защиты мотора M2  
- `$213xxxx;` - Сброс защиты мотора M3

### Тестирование UART (из предыдущих исправлений):
- `$200xxxx;` - Диагностика UART
- `$201xxxx;` - Тест отправки сообщений

## ⚠️ Предупреждения и рекомендации

### При максимальных скоростях:
1. **Контролируйте температуру** - моторы могут нагреваться
2. **Следите за вибрациями** - высокие частоты могут вызывать резонанс
3. **Проверяйте механику** - убедитесь что механизмы выдерживают нагрузку
4. **Используйте защиту** - система защиты поможет избежать поломок

### Признаки перегрузки:
- Мотор не достигает заданной позиции
- Превышение времени операции
- Странные звуки или вибрации
- Нагрев мотора или драйвера

### Действия при срабатывании защиты:
1. **Проверьте механику** - нет ли заеданий или препятствий
2. **Охладите систему** - дайте моторам остыть
3. **Сбросьте защиту** - используйте команды 211-213
4. **Снизьте скорость** - если проблемы повторяются

## 🔧 Настройка под конкретные условия

### Если моторы пищат:
- Увеличьте `step_delay_us` до 200-500 мкс
- Проверьте резонансные частоты механики

### Если моторы не тянут нагрузку:
- Увеличьте `pulse_width_us` для большего тока
- Проверьте настройки драйверов моторов
- Убедитесь в достаточном питании

### Если нужна еще большая скорость:
- Можно попробовать уменьшить до 50-60 мкс
- **ОСТОРОЖНО**: риск пропуска шагов!
- Обязательно тестируйте с нагрузкой

## 📊 Мониторинг производительности

### Показатели для отслеживания:
- Время выполнения операций
- Количество успешных/неудачных операций
- Температура моторов (если есть датчики)
- Точность позиционирования

### Оптимальные показатели:
- Успешность операций: > 95%
- Время операций: < 80% от максимального
- Температура: < 60°C
- Последовательные сбои: 0

## 🎯 Результаты оптимизации

### Увеличение скорости:
- **M1**: в 10 раз быстрее (500 Гц → 5000 Гц)
- **M2**: в 66 раз быстрее (50 Гц → 3333 Гц)
- **M3**: в 3.75 раза быстрее (1667 Гц → 6250 Гц)

### Преимущества:
- Значительное сокращение времени операций
- Более быстрая реакция системы
- Повышенная производительность

### Безопасность:
- Автоматическая защита от перегрузки
- Мониторинг состояния моторов
- Предупреждения о проблемах
- Возможность быстрого восстановления
