# STM32F103 Makefile

# Toolchain
CC = arm-none-eabi-gcc
OBJCOPY = arm-none-eabi-objcopy
SIZE = arm-none-eabi-size

# Project name
PROJECT = stm32_project

# MCU settings
MCU = cortex-m3
ARCH = armv7-m

# Directories
BUILD_DIR = build
SRC_DIR = .

# Source files
SOURCES = \
	main.c \
	UserFunction.c \
	Rcc.c \
	IO_gpio.c \
	I2C.c \
	lcd.c \
	Timers.c \
	RTE/Device/STM32F103ZE/startup_stm32f10x_hd.s \
	RTE/Device/STM32F103ZE/system_stm32f10x.c

# Include directories
INCLUDES = \
	-I. \
	-IRTE/Device/STM32F103ZE \
	-IRTE/_Target_1

# Compiler flags
CFLAGS = \
	-mcpu=$(MCU) \
	-mthumb \
	-mfloat-abi=soft \
	-DSTM32F103xE \
	-DUSE_STDPERIPH_DRIVER \
	-Wall \
	-Wextra \
	-O2 \
	-g \
	-ffunction-sections \
	-fdata-sections

# Linker flags
LDFLAGS = \
	-mcpu=$(MCU) \
	-mthumb \
	-specs=nano.specs \
	-specs=nosys.specs \
	-Wl,--gc-sections \
	-Wl,-Map=$(BUILD_DIR)/$(PROJECT).map

# Linker script (нужно найти .ld файл в проекте)
LDSCRIPT = STM32F103ZETx_FLASH.ld

# Object files
OBJECTS = $(addprefix $(BUILD_DIR)/,$(notdir $(SOURCES:.c=.o)))
OBJECTS := $(OBJECTS:.s=.o)

# VPATH для поиска исходников
vpath %.c .
vpath %.c RTE/Device/STM32F103ZE
vpath %.s RTE/Device/STM32F103ZE

# Default target
all: $(BUILD_DIR)/$(PROJECT).elf $(BUILD_DIR)/$(PROJECT).hex $(BUILD_DIR)/$(PROJECT).bin

# Create build directory
$(BUILD_DIR):
	mkdir $(BUILD_DIR)

# Compile C files
$(BUILD_DIR)/%.o: %.c | $(BUILD_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

$(BUILD_DIR)/%.o: */%.c | $(BUILD_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

$(BUILD_DIR)/%.o: */*/%.c | $(BUILD_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# Compile assembly files
$(BUILD_DIR)/%.o: %.s | $(BUILD_DIR)
	$(CC) $(CFLAGS) -c $< -o $@

$(BUILD_DIR)/%.o: */%.s | $(BUILD_DIR)
	$(CC) $(CFLAGS) -c $< -o $@

$(BUILD_DIR)/%.o: */*/%.s | $(BUILD_DIR)
	$(CC) $(CFLAGS) -c $< -o $@

# Link
$(BUILD_DIR)/$(PROJECT).elf: $(OBJECTS)
	$(CC) $(OBJECTS) $(LDFLAGS) -T$(LDSCRIPT) -o $@
	$(SIZE) $@

# Generate hex file
$(BUILD_DIR)/$(PROJECT).hex: $(BUILD_DIR)/$(PROJECT).elf
	$(OBJCOPY) -O ihex $< $@

# Generate binary file
$(BUILD_DIR)/$(PROJECT).bin: $(BUILD_DIR)/$(PROJECT).elf
	$(OBJCOPY) -O binary $< $@

# Clean
clean:
	rmdir /s /q $(BUILD_DIR) 2>nul || true

# Flash (если есть ST-Link)
flash: $(BUILD_DIR)/$(PROJECT).hex
	st-flash --format ihex write $<

.PHONY: all clean flash
