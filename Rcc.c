#ifndef RCC_H
#define RCC_H

#include "main.h"

#endif

#include "stm32f10x.h"
#include "Rcc.h"


void RCCInit(void)
{
/*************** Set up CLK for Peripherals ***********************/
	
	RCC->APB2ENR	|= RCC_APB2ENR_IOPAEN;  // Set up Clocks on PORTA
	
	RCC->APB2ENR	|= RCC_APB2ENR_IOPBEN;  // Set up Clocks on PORT�
	
	RCC->APB2ENR  |= RCC_APB2ENR_IOPCEN;    //Set up Clocks on PORT�
	
	RCC->APB2ENR  |= RCC_APB2ENR_IOPDEN;    //Set up Clocks on PORTD

	RCC->APB2ENR  |= RCC_APB2ENR_IOPEEN;    //Set up Clocks on PORTE	
	
	RCC->APB2ENR	|= RCC_APB2ENR_AFIOEN;	  //Set up Clocks on AFIO
	
//UART1:
	RCC->APB2ENR  |= RCC_APB2ENR_USART1EN;	//Set up Clocks on USART1 from APB2, max 72 MHz

	// Сначала отключаем USART1 для безопасной настройки
	USART1->CR1 &= ~USART_CR1_UE;

	// Настраиваем скорость передачи
	//USART1->BRR = 0xEA6;	// USART1 Baudrate 19200 at 72MHz
  USART1->BRR = 0x1D4C;	// USART1 Baudrate 9600 at 72MHz

	// Включаем USART1 с нужными настройками
	USART1->CR1 |= USART_CR1_UE | USART_CR1_TE | USART_CR1_RE | USART_CR1_RXNEIE;	// USART1 ON, TX ON, RX ON, RXNE Int ON

	//UART2:
	RCC->APB1ENR  |= RCC_APB1ENR_USART2EN; 	//Set up Clocks on USART2 CLK - APB1 max 36 MHz

	// Сначала отключаем USART2 для безопасной настройки
	USART2->CR1 &= ~USART_CR1_UE;

	// Настраиваем скорость передачи
  USART2->BRR = 0xEA6;	// USART2 Baudrate 9600 at 36 MHz

	// Включаем USART2 с нужными настройками
	USART2->CR1 |= USART_CR1_UE | USART_CR1_TE | USART_CR1_RE | USART_CR1_RXNEIE;	// USART2 ON, TX ON, RX ON, RXNE Int ON
	
	//UART3
	//RCC->APB1ENR  |= RCC_APB1ENR_USART3EN; 	//Set up Clocks on USART3 CLK - APB1 max 36 MHz
  //USART3->BRR |= 0xEA6;	// USART3 Baudrate 9600 at 36 MHz
	//USART3->CR1 |= USART_CR1_UE | USART_CR1_TE | USART_CR1_RE | USART_CR1_RXNEIE;	// USART3 ON, TX ON, RX ON, RXNE Int ON			
	
//I2C1:
	RCC->APB1ENR  |= RCC_APB1ENR_I2C1EN; 	 //Set up Clocks on I2C1
	
//I2C2:
	//RCC->APB1ENR  |= RCC_APB1ENR_I2C2EN; 	 //Set up Clocks on I2C2	
	
	//RCC->APB1ENR  |= RCC_APB1ENR_CAN1EN;	  //Set up Clocks on CAN1

//TIM1:
	RCC->APB2ENR  |= RCC_APB2ENR_TIM1EN;	  //Set up Clocks on TIM1	

//TIM2:
  RCC->APB1ENR  |= RCC_APB1ENR_TIM2EN;	  //Set up Clocks on  TIM2	

//TIM3:
  RCC->APB1ENR  |= RCC_APB1ENR_TIM3EN;	  //Set ON Clocking for TIM3	

//TIM4:
	RCC->APB1ENR  |= RCC_APB1ENR_TIM4EN;	  //Set up Clocks on TIM4
	
// Enable clocking for ADC1:
  //RCC->APB2ENR |= RCC_APB2ENR_ADC1EN;
	// Set up ADC prescaler == /6, so Fadc == 12Mhz
  //RCC->CFGR &= ~RCC_CFGR_ADCPRE_0;
  // ADCPRE[1:0] bits (ADC prescaler) == [10] == (/6)
  //RCC->CFGR |= RCC_CFGR_ADCPRE_1;	

}// End OF RCCInit(void)
