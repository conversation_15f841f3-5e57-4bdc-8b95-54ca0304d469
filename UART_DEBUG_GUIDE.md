# Руководство по отладке UART в прошивке CORDON-82

## Проблемы, которые были исправлены:

### 1. Неправильная очистка флагов в обработчиках прерываний
**Проблема:** В `USART1_IRQHandler` и `USART2_IRQHandler` флаги очищались неправильно:
```c
USART1->SR;  // Неправильно - просто чтение без сохранения
USART1->DR;  // Неправильно - чтение после обработки данных
```

**Исправление:** Правильная очистка флагов ошибок:
```c
// Проверяем и очищаем другие возможные флаги ошибок
if (USART1->SR & (USART_SR_ORE | USART_SR_NE | USART_SR_FE | USART_SR_PE)) {
    // Очищаем флаги ошибок чтением SR и DR
    volatile uint32_t temp = USART1->SR;
    temp = USART1->DR;
    (void)temp; // Подавляем предупреждение компилятора
}
```

### 2. Неправильная настройка регистра BRR
**Проблема:** Использование оператора `|=` вместо `=` для установки скорости:
```c
USART1->BRR |= 0x1D4C;  // Неправильно - может добавить биты к существующим
USART2->BRR |= 0xEA6;   // Неправильно
```

**Исправление:** Прямое присваивание значения:
```c
USART1->BRR = 0x1D4C;   // Правильно - устанавливает точное значение
USART2->BRR = 0xEA6;    // Правильно
```

### 3. Отсутствие безопасной инициализации UART
**Проблема:** UART настраивался без предварительного отключения.

**Исправление:** Добавлена безопасная последовательность инициализации:
```c
// Сначала отключаем USART1 для безопасной настройки
USART1->CR1 &= ~USART_CR1_UE;

// Настраиваем скорость передачи
USART1->BRR = 0x1D4C;

// Включаем USART1 с нужными настройками
USART1->CR1 |= USART_CR1_UE | USART_CR1_TE | USART_CR1_RE | USART_CR1_RXNEIE;
```

### 4. Неработающий USART2_IRQHandler
**Проблема:** Вся логика обработки была закомментирована, и была ошибка в регистре:
```c
//u8_CmdBuffer_2[u8_CmdIndex_2] = (uint8_t)USART1->DR; // Ошибка: USART1 вместо USART2
```

**Исправление:** Добавлена базовая обработка с правильным регистром:
```c
uint8_t received_byte = (uint8_t)USART2->DR;  // Правильно: USART2->DR
```

## Команды для тестирования:

### Бинарные команды:
- `$200xxxx;` - Диагностика UART (команда 200)
- `$201xxxx;` - Тест отправки сообщений (команда 201)

### JSON команды:
- `{"cmd":"ready"}` - Тест готовности системы
- `{"cmd":"about"}` - Информация о системе

## Настройки UART:

### UART1 (PA9/PA10):
- Скорость: 9600 бод
- Тактовая частота: 72 МГц (APB2)
- BRR = 0x1D4C
- Режим: TX + RX + прерывания по приему

### UART2 (PA2/PA3):
- Скорость: 9600 бод  
- Тактовая частота: 36 МГц (APB1)
- BRR = 0xEA6
- Режим: TX + RX + прерывания по приему

## Диагностическая функция:

Добавлена функция `UART_Diagnostics()` которая показывает на LCD:
- Состояние тактирования UART
- Включен ли UART
- Включены ли прерывания
- Статус флагов регистра SR
- Значение регистра BRR

## Проверка работы:

1. **Загрузите прошивку** - при старте автоматически покажется диагностика UART
2. **Отправьте команду 200** - `$200xxxx;` для повторной диагностики
3. **Отправьте команду 201** - `$201xxxx;` для теста отправки
4. **Попробуйте JSON команды** - `{"cmd":"ready"}`

## Возможные проблемы:

1. **Нет ответа на команды:**
   - Проверьте подключение TX/RX
   - Проверьте скорость передачи (9600 бод)
   - Проверьте, что используется правильный UART (UART1 на PA9/PA10)

2. **Искаженные данные:**
   - Проверьте тактовую частоту системы
   - Проверьте настройки стоп-битов и четности
   - Проверьте качество сигнала

3. **Прерывания не работают:**
   - Проверьте настройки NVIC
   - Проверьте, что прерывания включены глобально
   - Проверьте приоритеты прерываний

## Флаги статуса UART (отображаются в диагностике):

- R = RXNE (данные получены)
- T = TC (передача завершена)  
- E = TXE (буфер передачи пуст)
- O = ORE (переполнение приемника)
- F = FE (ошибка кадра)
- P = PE (ошибка четности)
