#ifndef MAIN_H
#define MAIN_H

#endif

#include "stdint.h"
#include "stm32f10x.h"
#include "IO_gpio.h"
//#include "I2C.h"
//#include "lcd.h"
#include "Timers.h"
#include "Rcc.h"
#include "UserFunction.h"
#include "lcd.h"
#include "motor_config.h"
#include "json_parser.h"

void USART1_IRQHandler(void);
void USART2_IRQHandler(void);
void Delay_mS(uint8_t );
void Delay_uS(uint16_t );
void Int_To_Char(uint16_t, uint8_t *);
void UART_Diagnostics(void);

// Функции защиты от перегрузки моторов
void Motor_Protection_Init(void);
void Motor_Protection_Start(uint8_t motor_id);
void Motor_Protection_Stop(uint8_t motor_id, uint8_t success);
uint8_t Motor_Protection_Check(uint8_t motor_id);
void Motor_Protection_Activate(uint8_t motor_id);
void Motor_Protection_Reset(uint8_t motor_id);
uint8_t Motor_Protection_GetStatus(uint8_t motor_id);
void Motor_Protection_Diagnostics(void);

extern uint8_t u8_Uart1_Cmd;
extern uint8_t u8_CmdIndex_1;
extern uint8_t u8_CmdBuffer_1[];
extern uint8_t u8_CmdNumber;
extern uint8_t u8_ReceivedCommand[];
extern uint8_t projectile_number;

extern uint16_t M3_StartStepTime;
extern uint16_t M3_StopStepTime;
extern uint16_t M3_DeltaStepTime;
extern uint16_t M3_StepNumber;

extern uint16_t M4_StartStepTime;
extern uint16_t M4_StopStepTime;
extern uint16_t M4_DeltaStepTime;


void SaveReceivedCommand(void);
void ClearCmdBuffer(void);